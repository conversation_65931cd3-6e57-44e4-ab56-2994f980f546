{"version": "2.0.0", "tasks": [{"label": "buildForDevice", "type": "shell", "command": "instinct3amoled45mm", "args": ["-d", "fenix7x", "-f", "monkey.jungle", "-o", "bin/madreface.prg", "-y", "${env:GARMIN_DEVELOPER_KEY}"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "clean", "type": "shell", "command": "rmdir", "args": ["/s", "/q", "bin"], "windows": {"command": "rmdir", "args": ["/s", "/q", "bin"]}, "linux": {"command": "rm", "args": ["-rf", "bin"]}, "osx": {"command": "rm", "args": ["-rf", "bin"]}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}